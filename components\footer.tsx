import Link from "next/link"
import SocialLinks from "./social-links"
import Image from "next/image"

export default function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-slate-800 text-white">
      <div className="max-w-6xl mx-auto px-4 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="md:col-span-2">
            <div className="flex items-center space-x-2 space-x-reverse mb-4">
              <Image
                src="/alaga-logo.png"
                alt="الاجا"
                width={120}
                height={40}
                className="h-10 w-auto brightness-0 invert"
              />
            </div>
            <p className="text-slate-300 mb-6 max-w-md">
              "نحن نستمع ونتصرف" - تحويل الحياة إلى الأبد من خلال التصميم الداخلي المدروس. الاجا تخلق مساحات جميلة
              وعملية تعزز نمط حياتك وتعكس قصتك الفريدة.
            </p>
            <SocialLinks />
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-semibold text-lg mb-4">روابط سريعة</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-slate-300 hover:text-alaga-400 transition-colors">
                  الرئيسية
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-slate-300 hover:text-alaga-400 transition-colors">
                  من نحن
                </Link>
              </li>
              <li>
                <Link href="/projects" className="text-slate-300 hover:text-alaga-400 transition-colors">
                  المشاريع
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-slate-300 hover:text-alaga-400 transition-colors">
                  تواصل معنا
                </Link>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="font-semibold text-lg mb-4">الخدمات</h3>
            <ul className="space-y-2 text-slate-300">
              <li>التصميم السكني</li>
              <li>المساحات التجارية</li>
              <li>تخطيط المساحات</li>
              <li>اختيار الأثاث</li>
              <li>استشارة الألوان</li>
              <li>إدارة المشاريع</li>
            </ul>
          </div>
        </div>

        <div className="border-t border-slate-700 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-slate-400 text-sm">© {currentYear} استوديو تصميم الاجا. جميع الحقوق محفوظة.</p>
          <div className="flex space-x-6 space-x-reverse mt-4 md:mt-0">
            <Link href="/privacy" className="text-slate-400 hover:text-alaga-400 text-sm transition-colors">
              سياسة الخصوصية
            </Link>
            <Link href="/terms" className="text-slate-400 hover:text-alaga-400 text-sm transition-colors">
              شروط الخدمة
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}
