"use server"

import { put } from "@vercel/blob"

export async function uploadImage(formData: FormData) {
  const file = formData.get("file") as File

  if (!file) {
    return { error: "لم يتم اختيار ملف" }
  }

  // التحقق من نوع الملف
  if (!file.type.startsWith("image/")) {
    return { error: "يجب أن يكون الملف صورة" }
  }

  // التحقق من حجم الملف (5MB max)
  if (file.size > 5 * 1024 * 1024) {
    return { error: "حجم الملف يجب أن يكون أقل من 5 ميجابايت" }
  }

  try {
    const blob = await put(`projects/${Date.now()}-${file.name}`, file, {
      access: "public",
    })

    return { success: true, url: blob.url }
  } catch (error) {
    console.error("Error uploading image:", error)
    return { error: "حدث خطأ في رفع الصورة" }
  }
}
