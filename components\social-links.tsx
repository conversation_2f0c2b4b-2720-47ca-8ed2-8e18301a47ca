import { Instagram, Facebook, Twitter, Linkedin, Youtube } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export default function SocialLinks() {
  const socialLinks = [
    {
      name: "Instagram",
      icon: Instagram,
      href: "https://instagram.com/yourdesignstudio",
      color: "hover:text-pink-500",
    },
    {
      name: "Facebook",
      icon: Facebook,
      href: "https://facebook.com/yourdesignstudio",
      color: "hover:text-blue-600",
    },
    {
      name: "Twitter",
      icon: Twitter,
      href: "https://twitter.com/yourdesignstudio",
      color: "hover:text-blue-400",
    },
    {
      name: "LinkedIn",
      icon: Linkedin,
      href: "https://linkedin.com/in/yourdesignstudio",
      color: "hover:text-blue-700",
    },
    {
      name: "YouTube",
      icon: Youtube,
      href: "https://youtube.com/@yourdesignstudio",
      color: "hover:text-red-600",
    },
  ]

  return (
    <div className="flex space-x-2">
      {socialLinks.map((social) => (
        <Button key={social.name} variant="ghost" size="icon" asChild className={`transition-colors ${social.color}`}>
          <a href={social.href} target="_blank" rel="noopener noreferrer" aria-label={`Follow on ${social.name}`}>
            <social.icon className="h-5 w-5" />
          </a>
        </Button>
      ))}
    </div>
  )
}
