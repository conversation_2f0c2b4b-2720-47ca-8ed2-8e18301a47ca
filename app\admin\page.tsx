"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Plus, Trash2, Loader2 } from "lucide-react"
import { addProject, deleteProject, getProjects } from "@/lib/projects"
import type { Project } from "@/lib/supabase"
import ImageUpload from "@/components/image-upload"
import { useRouter } from "next/navigation"

export default function AdminPage() {
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [imageUrls, setImageUrls] = useState<string[]>([])
  const router = useRouter()

  const [newProject, setNewProject] = useState({
    title: "",
    description: "",
  })

  useEffect(() => {
    loadProjects()
  }, [])

  const loadProjects = async () => {
    setLoading(true)
    const data = await getProjects()
    setProjects(data)
    setLoading(false)
  }

  const handleAddProject = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!newProject.title || !newProject.description || imageUrls.length === 0) {
      alert("جميع الحقول مطلوبة")
      return
    }

    setSubmitting(true)

    const formData = new FormData()
    formData.append("title", newProject.title)
    formData.append("description", newProject.description)
    imageUrls.forEach((url) => formData.append("imageUrls", url))

    const result = await addProject(formData)

    if (result.error) {
      alert(result.error)
    } else {
      alert("تم إضافة المشروع بنجاح!")
      setNewProject({ title: "", description: "" })
      setImageUrls([])
      await loadProjects()
    }

    setSubmitting(false)
  }

  const handleDeleteProject = async (id: number) => {
    if (!confirm("هل أنت متأكد من حذف هذا المشروع؟")) return

    const result = await deleteProject(id)

    if (result.error) {
      alert(result.error)
    } else {
      alert("تم حذف المشروع بنجاح!")
      await loadProjects()
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-slate-800 mb-2">لوحة تحكم الاجا</h1>
          <p className="text-slate-600">إدارة مشاريعك ومحتواك</p>
          <Button onClick={() => router.push("/projects")} variant="outline" className="mt-4">
            عرض الموقع
          </Button>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* إضافة مشروع جديد */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                إضافة مشروع جديد
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleAddProject} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">عنوان المشروع *</Label>
                  <Input
                    id="title"
                    value={newProject.title}
                    onChange={(e) => setNewProject({ ...newProject, title: e.target.value })}
                    placeholder="أدخل عنوان المشروع"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">وصف المشروع *</Label>
                  <Textarea
                    id="description"
                    value={newProject.description}
                    onChange={(e) => setNewProject({ ...newProject, description: e.target.value })}
                    placeholder="وصف تفصيلي للمشروع..."
                    rows={4}
                    required
                  />
                </div>

                <ImageUpload onImagesChange={setImageUrls} maxImages={10} />

                <Button type="submit" className="w-full bg-alaga-400 hover:bg-alaga-500" disabled={submitting}>
                  {submitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      جاري الإضافة...
                    </>
                  ) : (
                    "إضافة مشروع"
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* المشاريع الموجودة */}
          <Card>
            <CardHeader>
              <CardTitle>المشاريع الموجودة ({projects.length})</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : (
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {projects.map((project) => (
                    <div key={project.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div className="flex-1">
                          <h3 className="font-semibold text-slate-800">{project.title}</h3>
                          <p className="text-sm text-slate-600 mt-1 line-clamp-2">{project.description}</p>
                        </div>
                        <div className="flex gap-2 ml-4">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteProject(project.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <Badge variant="secondary">
                          {project.images.length} {project.images.length === 1 ? "صورة" : "صور"}
                        </Badge>
                        <span className="text-xs text-slate-500">
                          {new Date(project.created_at).toLocaleDateString("ar-EG")}
                        </span>
                      </div>
                    </div>
                  ))}

                  {projects.length === 0 && (
                    <div className="text-center py-8 text-slate-500">لا توجد مشاريع حتى الآن</div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid md:grid-cols-4 gap-4 mt-8">
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-slate-800">{projects.length}</div>
              <div className="text-sm text-slate-600">إجمالي المشاريع</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-slate-800">
                {projects.reduce((total, project) => total + project.images.length, 0)}
              </div>
              <div className="text-sm text-slate-600">إجمالي الصور</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-slate-800">
                {
                  projects.filter((p) => new Date(p.created_at) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000))
                    .length
                }
              </div>
              <div className="text-sm text-slate-600">مشاريع هذا الشهر</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-slate-800">
                {Math.round(
                  projects.reduce((total, project) => total + project.images.length, 0) / Math.max(projects.length, 1),
                )}
              </div>
              <div className="text-sm text-slate-600">متوسط الصور/مشروع</div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
