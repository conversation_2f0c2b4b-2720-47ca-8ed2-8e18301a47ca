import { Button } from "components/ui/button"
import { Card, CardContent } from "components/ui/card"
import { ArrowLeft, Award, Users, Palette } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import Navbar from "components/navbar"
import Footer from "components/footer"
import SocialLinks from "components/social-links"

export default function HomePage() {
  const featuredProjects = [
    {
      id: 1,
      title: "غرفة معيشة عصرية",
      category: "سكني",
      image: "/placeholder.svg?height=400&width=600",
      description: "تصميم معاصر بخطوط نظيفة ومواد طبيعية",
    },
    {
      id: 2,
      title: "مكتب تنفيذي",
      category: "تجاري",
      image: "/placeholder.svg?height=400&width=600",
      description: "مساحة عمل مهنية مع حلول أثاث مريحة",
    },
    {
      id: 3,
      title: "غرفة نوم فاخرة",
      category: "سكني",
      image: "/placeholder.svg?height=400&width=600",
      description: "تصميم غرفة نوم أنيق مع قطع أثاث مخصصة",
    },
  ]

  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <Image src="/hero-background.jpg" alt="تصميم داخلي أنيق" fill className="object-cover" priority />
          <div className="absolute inset-0 bg-black/40"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 drop-shadow-lg">
            تصميم الاثاث الداخلي
            <span className="block text-alaga-400">من الاجا</span>
          </h1>
          <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-2xl mx-auto drop-shadow-md">
            إنشاء مساحات جميلة وعملية تعكس أسلوبك الفريد وتعزز نمط حياتك
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="bg-alaga-400 hover:bg-alaga-500 text-slate-800 font-semibold">
              <Link href="/projects">
                <ArrowLeft className="mr-2 h-5 w-5" />
                عرض المشاريع
              </Link>
            </Button>
            <Button
              asChild
              variant="outline"
              size="lg"
              className="border-white text-white hover:bg-white hover:text-slate-800 backdrop-blur-sm"
            >
              <Link href="/contact">تواصل الان</Link>
            </Button>
          </div>
        </div>

        {/* Social Links */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
          <SocialLinks />
        </div>
      </section>

      {/* About Preview */}
      <section className="py-20 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-bold text-slate-800 mb-6">صناعة مساحات تلهم</h2>
              <p className="text-lg text-slate-600 mb-6">
                مع أكثر من عقد من الخبرة في تصميم الاثاث الداخلي، أتخصص في إنشاء مساحات متناغمة تمزج بين الوظيفة
                والجاذبية الجمالية. كل مشروع هو رحلة فريدة لتحويل رؤيتك إلى واقع.
              </p>
              <div className="grid grid-cols-3 gap-6 mb-8">
                <div className="text-center">
                  <Award className="h-8 w-8 text-alaga-500 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-slate-800">50+</div>
                  <div className="text-sm text-slate-600">مشروع</div>
                </div>
                <div className="text-center">
                  <Users className="h-8 w-8 text-alaga-500 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-slate-800">40+</div>
                  <div className="text-sm text-slate-600">عميل سعيد</div>
                </div>
                <div className="text-center">
                  <Palette className="h-8 w-8 text-alaga-500 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-slate-800">10+</div>
                  <div className="text-sm text-slate-600">سنوات خبرة</div>
                </div>
              </div>
              <Button asChild variant="outline" className="border-amber-600 text-amber-600 hover:bg-amber-50">
                <Link href="/about">تعرف علي أكثر</Link>
              </Button>
            </div>
            <div className="relative">
              <Image
                src="/placeholder.svg?height=500&width=400"
                alt="مصمم داخلي"
                width={400}
                height={500}
                className="rounded-lg shadow-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Featured Projects */}
      <section className="py-20 bg-slate-50 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-slate-800 mb-4">المشاريع المميزة</h2>
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              اكتشف بعض أعمالي الحديثة التي تعرض أساليب تصميم متنوعة وحلول مبتكرة
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {featuredProjects.map((project) => (
              <Card key={project.id} className="group cursor-pointer hover:shadow-xl transition-all duration-300">
                <CardContent className="p-0">
                  <div className="relative overflow-hidden">
                    <Image
                      src={project.image || "/placeholder.svg"}
                      alt={project.title}
                      width={600}
                      height={400}
                      className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute top-4 right-4">
                      <span className="bg-alaga-400 text-white px-3 py-1 rounded-full text-sm font-medium">
                        {project.category}
                      </span>
                    </div>
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-slate-800 mb-2">{project.title}</h3>
                    <p className="text-slate-600">{project.description}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-12">
            <Button asChild size="lg" className="bg-alaga-400 hover:bg-alaga-500">
              <Link href="/projects">
                <ArrowLeft className="mr-2 h-5 w-5" />
                عرض جميع المشاريع
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-slate-800 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">هل أنت مستعد لتحويل مساحتك؟</h2>
          <p className="text-xl text-slate-300 mb-8">دعنا نناقش رؤيتك ونخلق شيئًا استثنائيًا معًا</p>
          <Button asChild size="lg" className="bg-alaga-400 hover:bg-alaga-500 text-slate-800 font-semibold">
            <Link href="/contact">ابدأ مشروعك</Link>
          </Button>
        </div>
      </section>

      <Footer />
    </div>
  )
}
