"use server"

import { supabase } from "./supabase"
import { revalidatePath } from "next/cache"

export async function getProjects() {
  const { data, error } = await supabase.from("projects").select("*").order("created_at", { ascending: false })

  if (error) {
    console.error("Error fetching projects:", error)
    return []
  }

  return data || []
}

export async function addProject(formData: FormData) {
  const title = formData.get("title") as string
  const description = formData.get("description") as string
  const imageUrls = formData.getAll("imageUrls") as string[]

  if (!title || !description || imageUrls.length === 0) {
    return { error: "جميع الحقول مطلوبة" }
  }

  const { data, error } = await supabase
    .from("projects")
    .insert([
      {
        title,
        description,
        images: imageUrls.filter((url) => url.trim() !== ""),
      },
    ])
    .select()

  if (error) {
    console.error("Error adding project:", error)
    return { error: "حدث خطأ في إضافة المشروع" }
  }

  revalidatePath("/projects")
  revalidatePath("/admin")

  return { success: true, data }
}

export async function deleteProject(id: number) {
  const { error } = await supabase.from("projects").delete().eq("id", id)

  if (error) {
    console.error("Error deleting project:", error)
    return { error: "حدث خطأ في حذف المشروع" }
  }

  revalidatePath("/projects")
  revalidatePath("/admin")

  return { success: true }
}
