import { Card, CardContent } from "@/components/ui/card"
import { Award, BookOpen, Heart, Lightbulb } from "lucide-react"
import Image from "next/image"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"

export default function AboutPage() {
  const values = [
    {
      icon: Heart,
      title: "الشغف",
      description: "يتم التعامل مع كل مشروع بحماس حقيقي والتزام بالتميز",
    },
    {
      icon: Lightbulb,
      title: "الابتكار",
      description: "الجمع بين مبادئ التصميم الخالدة والاتجاهات والتقنيات المعاصرة",
    },
    {
      icon: Award,
      title: "الجودة",
      description: "اهتمام لا يتزعزع بالتفاصيل والالتزام بالحرفية المتفوقة",
    },
    {
      icon: BookOpen,
      title: "التعاون",
      description: "العمل بشكل وثيق مع العملاء لفهم وتحقيق رؤيتهم الفريدة",
    },
  ]

  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      {/* Hero Section */}
      <section className="py-20 px-4 bg-gradient-to-br from-alaga-50 to-alaga-100">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-5xl font-bold text-slate-800 mb-6">عن الاجا</h1>
              <p className="text-xl text-slate-600 mb-6">
                الاجا مش بس اسم… الاجا أسلوب حياة. بنؤمن إن كل ركن في بيتك يستحق يبقى ليه طابع خاص، ولمسة فنية تريحك
                وتبهجك كل يوم.
              </p>
              <p className="text-lg text-slate-600 mb-6">
                <span className="font-semibold text-alaga-600">"الاجا… لما الذوق يبقى فن والمعيشة تبقى مريحة"</span>
              </p>
            </div>
            <div className="relative">
              <Image
                src="/placeholder.svg?height=600&width=500"
                alt="فلسفة تصميم الاجا"
                width={500}
                height={600}
                className="rounded-lg shadow-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* My Story */}
      <section className="py-20 px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl font-bold text-slate-800 mb-8 text-center">قصة الاجا</h2>
          <div className="prose prose-lg max-w-none text-slate-600">
            <p className="text-xl leading-relaxed mb-6">
              بدأنا من شغف بسيط بحبنا للتفاصيل، وكل قطعة أثاث بنصممها كانت حكاية لوحدها. من أول مشروع صغير لحد ما بقينا
              نشتغل على بيوت كاملة… كنا دايمًا بنسعى إن التصميم يعبر عنك ويشبِهك، مش بس يبقى شكل حلو.
            </p>

            <h3 className="text-2xl font-bold text-slate-800 mb-4">ليه تختار الاجا؟</h3>
            <ul className="text-lg leading-relaxed space-y-3">
              <li>• بنصمملك على ذوقك، مش كتالوجات جاهزة.</li>
              <li>• بنستخدم خامات تعيش وتستحمل، من غير ما تضحّي بالشياكة.</li>
              <li>• بنهتم بأدق التفاصيل، لأن التفاصيل هي اللي بتفرق.</li>
            </ul>
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="py-20 bg-slate-50 px-4">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-slate-800 mb-16 text-center">أهدافنا</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-8">
                  <value.icon className="h-12 w-12 text-alaga-500 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-slate-800 mb-3">{value.title}</h3>
                  <p className="text-slate-600">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Process */}
      <section className="py-20 px-4">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold text-slate-800 mb-16 text-center">خطوات العمل في الاجا</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-alaga-400 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6">
                1
              </div>
              <h3 className="text-xl font-bold text-slate-800 mb-4">مرحلة الاكتشاف</h3>
              <p className="text-slate-600">
                نبدأ بجلسة نقاش بنفهم فيها أسلوب حياتك، ذوقك، واحتياجاتك من المساحة. كل تفصيلة تهمنا، لأنها هي اللي
                بتحدد الشخصية الحقيقية للتصميم.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-alaga-400 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6">
                2
              </div>
              <h3 className="text-xl font-bold text-slate-800 mb-4">مرحلة التصميم</h3>
              <p className="text-slate-600">
                بنرجع لك بمقترحات تصميم مدروسة، لوحات إلهام (Mood Boards)، وتصورات ثلاثية الأبعاد، علشان تشوف رؤيتك
                بتتحول لحاجة ملموسة قدامك.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-alaga-400 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-6">
                3
              </div>
              <h3 className="text-xl font-bold text-slate-800 mb-4">مرحلة التنفيذ</h3>
              <p className="text-slate-600">
                بنتابع كل خطوة في التنفيذ بدقة، ونشرف على التفاصيل كلها علشان نضمن إن النتيجة النهائية تطلع بالشكل اللي
                يرضيك… ويمكن كمان تبهرَك.
              </p>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
