"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { MapPin, Phone, Mail, Clock } from "lucide-react"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import SocialLinks from "@/components/social-links"

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    projectType: "",
    budget: "",
    message: "",
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission here
    console.log("Form submitted:", formData)
    alert("شكرًا لك على رسالتك! سأعود إليك قريبًا.")
  }

  const handleChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      {/* Hero Section */}
      <section className="py-20 px-4 bg-gradient-to-br from-slate-50 to-stone-100">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-5xl font-bold text-slate-800 mb-6">تواصل مع الاجا</h1>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            هل أنت مستعد لتحويل مساحتك؟ لنبدأ بالاستماع. شارك رؤيتك معنا وشاهد كيف نتصرف لتحقيق أحلامك من خلال التصميم
            الداخلي المدروس.
          </p>
        </div>
      </section>

      {/* Contact Form & Info */}
      <section className="py-20 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-3 gap-12">
            {/* Contact Form */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl text-slate-800">ابدأ مشروعك</CardTitle>
                  <p className="text-slate-600">املأ النموذج أدناه وسأعود إليك خلال 24 ساعة.</p>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">الاسم الكامل *</Label>
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) => handleChange("name", e.target.value)}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">عنوان البريد الإلكتروني *</Label>
                        <Input
                          id="email"
                          type="email"
                          value={formData.email}
                          onChange={(e) => handleChange("email", e.target.value)}
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="phone">رقم الهاتف</Label>
                      <Input
                        id="phone"
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => handleChange("phone", e.target.value)}
                      />
                    </div>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="projectType">نوع المشروع</Label>
                        <Select onValueChange={(value) => handleChange("projectType", value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر نوع المشروع" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="residential">سكني</SelectItem>
                            <SelectItem value="commercial">تجاري</SelectItem>
                            <SelectItem value="hospitality">ضيافة</SelectItem>
                            <SelectItem value="retail">تجزئة</SelectItem>
                            <SelectItem value="consultation">استشارة فقط</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="budget">نطاق الميزانية</Label>
                        <Select onValueChange={(value) => handleChange("budget", value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر نطاق الميزانية" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="under-10k">أقل من 10,000 دولار</SelectItem>
                            <SelectItem value="10k-25k">10,000 - 25,000 دولار</SelectItem>
                            <SelectItem value="25k-50k">25,000 - 50,000 دولار</SelectItem>
                            <SelectItem value="50k-100k">50,000 - 100,000 دولار</SelectItem>
                            <SelectItem value="over-100k">أكثر من 100,000 دولار</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="message">تفاصيل المشروع *</Label>
                      <Textarea
                        id="message"
                        rows={6}
                        placeholder="أخبرني عن مشروعك والجدول الزمني وأي متطلبات محددة..."
                        value={formData.message}
                        onChange={(e) => handleChange("message", e.target.value)}
                        required
                      />
                    </div>

                    <Button type="submit" size="lg" className="w-full bg-alaga-400 hover:bg-alaga-500">
                      إرسال الرسالة
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>

            {/* Contact Info */}
            <div className="space-y-8">
              <Card>
                <CardHeader>
                  <CardTitle className="text-xl text-slate-800">معلومات الاتصال</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-start space-x-3 space-x-reverse">
                    <MapPin className="h-5 w-5 text-alaga-500 mt-1" />
                    <div>
                      <p className="font-medium text-slate-800">عنوان الاستوديو</p>
                      <p className="text-slate-600">
                        123 شارع التصميم
                        <br />
                        الحي الإبداعي
                        <br />
                        المدينة، الولاية 12345
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3 space-x-reverse">
                    <Phone className="h-5 w-5 text-alaga-500 mt-1" />
                    <div>
                      <p className="font-medium text-slate-800">الهاتف</p>
                      <p className="text-slate-600">+****************</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3 space-x-reverse">
                    <Mail className="h-5 w-5 text-alaga-500 mt-1" />
                    <div>
                      <p className="font-medium text-slate-800">البريد الإلكتروني</p>
                      <p className="text-slate-600"><EMAIL></p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3 space-x-reverse">
                    <Clock className="h-5 w-5 text-alaga-500 mt-1" />
                    <div>
                      <p className="font-medium text-slate-800">ساعات العمل</p>
                      <p className="text-slate-600">
                        الاثنين - الجمعة: 9:00 ص - 6:00 م
                        <br />
                        السبت: 10:00 ص - 4:00 م
                        <br />
                        الأحد: بموعد مسبق
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-xl text-slate-800">تابع أعمالي</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-slate-600 mb-4">ابق على اطلاع بأحدث مشاريعي وإلهام التصميم.</p>
                  <SocialLinks />
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
