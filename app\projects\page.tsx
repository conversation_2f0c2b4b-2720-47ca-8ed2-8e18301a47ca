import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import { getProjects } from "@/lib/projects"

export default async function ProjectsPage() {
  const projects = await getProjects()

  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      {/* Hero Section */}
      <section className="py-20 px-4 bg-gradient-to-br from-alaga-50 to-alaga-100">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-5xl font-bold text-slate-800 mb-6">مشاريعنا</h1>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto mb-4">
            استكشف مجموعة من أعمالنا المميزة في تصميم الأثاث الداخلي
          </p>
          <p className="text-lg text-slate-600 max-w-3xl mx-auto">
            كل مشروع بيحكي قصة خاصة، وبيعكس إزاي بنترجم رؤيتك إلى واقع… لأن في الاجا، دايمًا بنسمعك، وبنشتغل على اللي
            يناسبك فعلًا.
          </p>
        </div>
      </section>

      {/* Projects Gallery */}
      <section className="py-20 px-4">
        <div className="max-w-6xl mx-auto">
          {projects.length === 0 ? (
            <div className="text-center py-20">
              <p className="text-xl text-slate-600">لا توجد مشاريع حتى الآن</p>
            </div>
          ) : (
            <div className="space-y-16">
              {projects.map((project) => (
                <div key={project.id} className="group">
                  <Card className="overflow-hidden hover:shadow-2xl transition-all duration-500">
                    <CardContent className="p-0">
                      {/* Project Images */}
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                        {project.images.map((image, index) => (
                          <div
                            key={index}
                            className={`relative overflow-hidden ${
                              index === 0 && project.images.length > 1 ? "md:col-span-2 lg:col-span-2" : ""
                            }`}
                          >
                            <Image
                              src={image || "/placeholder.svg"}
                              alt={`${project.title} - صورة ${index + 1}`}
                              width={600}
                              height={400}
                              className="w-full h-64 md:h-80 object-cover group-hover:scale-105 transition-transform duration-500"
                            />
                          </div>
                        ))}
                      </div>

                      {/* Project Info */}
                      <div className="p-8 bg-gradient-to-r from-slate-50 to-white">
                        <h3 className="text-2xl font-bold text-slate-800 mb-4">{project.title}</h3>
                        <p className="text-slate-600 text-lg leading-relaxed">{project.description}</p>

                        {/* Project Stats */}
                        <div className="flex items-center justify-between mt-6 pt-6 border-t border-slate-200">
                          <span className="text-sm text-slate-500">
                            {project.images.length} {project.images.length === 1 ? "صورة" : "صور"}
                          </span>
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <span className="w-2 h-2 bg-alaga-400 rounded-full"></span>
                            <span className="text-sm text-alaga-600 font-medium">مشروع مكتمل</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-slate-800 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">هل لديك مشروع في ذهنك؟</h2>
          <p className="text-xl text-slate-300 mb-8">دعنا نساعدك في تحويل أفكارك إلى واقع جميل ووظيفي</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="inline-flex items-center justify-center px-8 py-3 bg-alaga-400 hover:bg-alaga-500 text-slate-800 font-semibold rounded-lg transition-colors"
            >
              ابدأ مشروعك الآن
            </a>
            <a
              href="tel:+1234567890"
              className="inline-flex items-center justify-center px-8 py-3 border border-slate-600 text-white hover:bg-slate-700 font-semibold rounded-lg transition-colors"
            >
              اتصل بنا مباشرة
            </a>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
