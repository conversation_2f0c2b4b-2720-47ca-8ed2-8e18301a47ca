"use client"

import type React from "react"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Upload, X } from "lucide-react"
import { uploadImage } from "@/lib/upload"
import Image from "next/image"

interface ImageUploadProps {
  onImagesChange: (urls: string[]) => void
  maxImages?: number
}

export default function ImageUpload({ onImagesChange, maxImages = 10 }: ImageUploadProps) {
  const [images, setImages] = useState<string[]>([])
  const [uploading, setUploading] = useState(false)

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    if (images.length + files.length > maxImages) {
      alert(`يمكنك رفع ${maxImages} صور كحد أقصى`)
      return
    }

    setUploading(true)

    try {
      const uploadPromises = Array.from(files).map(async (file) => {
        const formData = new FormData()
        formData.append("file", file)
        return uploadImage(formData)
      })

      const results = await Promise.all(uploadPromises)
      const successfulUploads = results.filter((result) => result.success).map((result) => result.url!)

      const newImages = [...images, ...successfulUploads]
      setImages(newImages)
      onImagesChange(newImages)

      // التحقق من وجود أخطاء
      const errors = results.filter((result) => result.error)
      if (errors.length > 0) {
        alert(`حدث خطأ في رفع ${errors.length} من الصور`)
      }
    } catch (error) {
      alert("حدث خطأ في رفع الصور")
    } finally {
      setUploading(false)
      // إعادة تعيين input
      event.target.value = ""
    }
  }

  const removeImage = (index: number) => {
    const newImages = images.filter((_, i) => i !== index)
    setImages(newImages)
    onImagesChange(newImages)
  }

  return (
    <div className="space-y-4">
      <Label>
        صور المشروع ({images.length}/{maxImages})
      </Label>

      {/* منطقة رفع الصور */}
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
        <Input
          type="file"
          multiple
          accept="image/*"
          onChange={handleFileUpload}
          disabled={uploading || images.length >= maxImages}
          className="hidden"
          id="image-upload"
        />
        <Label
          htmlFor="image-upload"
          className={`cursor-pointer flex flex-col items-center space-y-2 ${
            uploading || images.length >= maxImages ? "opacity-50 cursor-not-allowed" : ""
          }`}
        >
          <Upload className="h-8 w-8 text-gray-400" />
          <span className="text-sm text-gray-600">
            {uploading ? "جاري الرفع..." : "اضغط لاختيار الصور أو اسحبها هنا"}
          </span>
          <span className="text-xs text-gray-500">PNG, JPG, JPEG (حد أقصى 5MB لكل صورة)</span>
        </Label>
      </div>

      {/* عرض الصور المرفوعة */}
      {images.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {images.map((url, index) => (
            <div key={index} className="relative group">
              <div className="aspect-square relative overflow-hidden rounded-lg border">
                <Image src={url || "/placeholder.svg"} alt={`صورة ${index + 1}`} fill className="object-cover" />
              </div>
              <Button
                type="button"
                variant="destructive"
                size="sm"
                className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => removeImage(index)}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
